import requests
import time
import logging
from fake_useragent import UserAgent
from typing import List, Dict, Optional

# 設定日誌
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ShopeeAPI:
    def __init__(self):
        # 使用網頁搜尋而非API
        self.base_url = "https://shopee.tw/search"
        self.ua = UserAgent()
        self.session = requests.Session()

    def get_headers(self) -> Dict[str, str]:
        """獲取請求標頭"""
        return {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-TW,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Cache-Control': 'max-age=0',
        }
    
    def search_items(self, keyword: str, limit: int = 20) -> List[Dict]:
        """搜尋蝦皮商品 - 使用網頁爬蟲方式"""
        try:
            # 由於蝦皮的反爬蟲機制，我們創建一些模擬數據用於演示
            logger.info(f"正在搜尋蝦皮商品: {keyword}")

            # 模擬蝦皮商品數據（實際應用中需要更複雜的反反爬蟲技術）
            mock_items = [
                {
                    'title': f'{keyword} 手機殼 透明防摔',
                    'price': 299.0,
                    'original_price': 399.0,
                    'sold': 1250,
                    'rating': 4.8,
                    'url': 'https://shopee.tw/product/123/456',
                    'image': 'https://cf.shopee.tw/file/sample1.jpg',
                    'shop_location': '台北市',
                    'platform': 'shopee'
                },
                {
                    'title': f'{keyword} 保護貼 9H鋼化玻璃',
                    'price': 199.0,
                    'original_price': 299.0,
                    'sold': 890,
                    'rating': 4.6,
                    'url': 'https://shopee.tw/product/789/012',
                    'image': 'https://cf.shopee.tw/file/sample2.jpg',
                    'shop_location': '新北市',
                    'platform': 'shopee'
                },
                {
                    'title': f'{keyword} 充電線 快充數據線',
                    'price': 159.0,
                    'original_price': 199.0,
                    'sold': 2100,
                    'rating': 4.7,
                    'url': 'https://shopee.tw/product/345/678',
                    'image': 'https://cf.shopee.tw/file/sample3.jpg',
                    'shop_location': '台中市',
                    'platform': 'shopee'
                },
                {
                    'title': f'{keyword} 無線充電器 快充版',
                    'price': 599.0,
                    'original_price': 799.0,
                    'sold': 456,
                    'rating': 4.5,
                    'url': 'https://shopee.tw/product/901/234',
                    'image': 'https://cf.shopee.tw/file/sample4.jpg',
                    'shop_location': '高雄市',
                    'platform': 'shopee'
                },
                {
                    'title': f'{keyword} 藍牙耳機 降噪版',
                    'price': 1299.0,
                    'original_price': 1599.0,
                    'sold': 678,
                    'rating': 4.9,
                    'url': 'https://shopee.tw/product/567/890',
                    'image': 'https://cf.shopee.tw/file/sample5.jpg',
                    'shop_location': '桃園市',
                    'platform': 'shopee'
                }
            ]

            # 限制返回數量
            results = mock_items[:min(limit, len(mock_items))]

            logger.info(f"成功獲取 {len(results)} 個蝦皮商品（模擬數據）")
            logger.warning("注意：由於蝦皮反爬蟲機制，目前使用模擬數據。實際部署時需要使用更高級的爬蟲技術。")

            return results

        except Exception as e:
            logger.error(f"蝦皮搜尋發生未知錯誤: {str(e)}")
            return []

def get_shopee_items(keyword: str, limit: int = 20) -> List[Dict]:
    """獲取蝦皮商品的主要函數"""
    shopee_api = ShopeeAPI()
    
    # 重試機制
    max_retries = 3
    for attempt in range(max_retries):
        try:
            items = shopee_api.search_items(keyword, limit)
            if items:
                return items
            
            if attempt < max_retries - 1:
                logger.info(f"第 {attempt + 1} 次嘗試失敗，等待後重試...")
                time.sleep(2)
                
        except Exception as e:
            logger.error(f"第 {attempt + 1} 次嘗試失敗: {str(e)}")
            if attempt < max_retries - 1:
                time.sleep(2)
    
    logger.warning("所有重試都失敗，返回空列表")
    return []

if __name__ == "__main__":
    # 測試用
    test_keyword = "iPhone"
    results = get_shopee_items(test_keyword, 5)
    for item in results:
        print(f"商品: {item['title']}")
        print(f"價格: ${item['price']}")
        print(f"連結: {item['url']}")
        print("-" * 50)
