import re
import logging
from typing import List, Dict, Tuple
from difflib import SequenceMatcher

# 設定日誌
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def clean_title(title: str) -> str:
    """清理商品標題，移除特殊字符和多餘空格"""
    if not title:
        return ""
    
    # 移除HTML標籤
    title = re.sub(r'<[^>]+>', '', title)
    
    # 移除特殊字符，保留中文、英文、數字和基本符號
    title = re.sub(r'[^\w\s\u4e00-\u9fff\-\+\(\)\[\]]', ' ', title)
    
    # 移除多餘空格
    title = re.sub(r'\s+', ' ', title).strip()
    
    return title.lower()

def extract_keywords(title: str) -> List[str]:
    """從標題中提取關鍵詞"""
    cleaned_title = clean_title(title)
    
    # 分割成詞語
    words = cleaned_title.split()
    
    # 過濾掉太短的詞語和常見的無意義詞語
    stop_words = {'的', '和', '與', '或', '在', '有', '是', '了', '個', '一', '二', '三', '四', '五',
                  'the', 'and', 'or', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by'}
    
    keywords = []
    for word in words:
        if len(word) >= 2 and word not in stop_words:
            keywords.append(word)
    
    return keywords

def calculate_similarity(title1: str, title2: str) -> float:
    """計算兩個標題的相似度"""
    if not title1 or not title2:
        return 0.0
    
    # 清理標題
    clean1 = clean_title(title1)
    clean2 = clean_title(title2)
    
    # 使用SequenceMatcher計算相似度
    similarity = SequenceMatcher(None, clean1, clean2).ratio()
    
    # 提取關鍵詞進行額外比較
    keywords1 = set(extract_keywords(title1))
    keywords2 = set(extract_keywords(title2))
    
    if keywords1 and keywords2:
        # 計算關鍵詞交集比例
        intersection = keywords1.intersection(keywords2)
        union = keywords1.union(keywords2)
        keyword_similarity = len(intersection) / len(union) if union else 0
        
        # 綜合相似度 (70% 文字相似度 + 30% 關鍵詞相似度)
        similarity = similarity * 0.7 + keyword_similarity * 0.3
    
    return similarity

def find_best_matches(shopee_items: List[Dict], taobao_items: List[Dict], 
                     similarity_threshold: float = 0.3) -> List[Tuple[Dict, Dict, float]]:
    """找到最佳匹配的商品對"""
    matches = []
    used_taobao_indices = set()
    
    for shopee_item in shopee_items:
        best_match = None
        best_similarity = 0.0
        best_taobao_index = -1
        
        for i, taobao_item in enumerate(taobao_items):
            if i in used_taobao_indices:
                continue
                
            similarity = calculate_similarity(shopee_item['title'], taobao_item['title'])
            
            if similarity > best_similarity and similarity >= similarity_threshold:
                best_similarity = similarity
                best_match = taobao_item
                best_taobao_index = i
        
        if best_match:
            matches.append((shopee_item, best_match, best_similarity))
            used_taobao_indices.add(best_taobao_index)
    
    # 按相似度排序
    matches.sort(key=lambda x: x[2], reverse=True)
    
    return matches

def calculate_price_difference(shopee_price: float, taobao_price: float) -> Dict[str, float]:
    """計算價格差異"""
    if shopee_price <= 0 or taobao_price <= 0:
        return {'diff': 0.0, 'ratio': 0.0, 'percentage': 0.0}
    
    diff = shopee_price - taobao_price
    ratio = shopee_price / taobao_price
    percentage = (diff / taobao_price) * 100
    
    return {
        'diff': round(diff, 2),
        'ratio': round(ratio, 2),
        'percentage': round(percentage, 2)
    }

def match_and_compare(shopee_items: List[Dict], taobao_items: List[Dict]) -> List[Dict]:
    """匹配商品並比較價格"""
    if not shopee_items or not taobao_items:
        logger.warning("蝦皮或淘寶商品列表為空")
        return []
    
    logger.info(f"開始匹配商品: 蝦皮 {len(shopee_items)} 個, 淘寶 {len(taobao_items)} 個")
    
    # 找到最佳匹配
    matches = find_best_matches(shopee_items, taobao_items)
    
    results = []
    for shopee_item, taobao_item, similarity in matches:
        # 計算價格差異
        price_diff = calculate_price_difference(shopee_item['price'], taobao_item['price'])
        
        # 組合結果
        result = {
            'title': shopee_item['title'],
            'shopee_title': shopee_item['title'],
            'taobao_title': taobao_item['title'],
            'shopee_price': shopee_item['price'],
            'taobao_price': taobao_item['price'],
            'diff': price_diff['diff'],
            'ratio': price_diff['ratio'],
            'percentage': price_diff['percentage'],
            'similarity': round(similarity, 3),
            'shopee_url': shopee_item.get('url', ''),
            'taobao_url': taobao_item.get('url', ''),
            'shopee_image': shopee_item.get('image', ''),
            'taobao_image': taobao_item.get('image', ''),
            'shopee_sold': shopee_item.get('sold', 0),
            'taobao_sold': taobao_item.get('sold', 0),
        }
        
        results.append(result)
    
    logger.info(f"成功匹配 {len(results)} 個商品對")
    
    # 按價格差異排序（價差最大的在前面）
    results.sort(key=lambda x: x['diff'], reverse=True)
    
    return results

def format_currency(amount: float) -> str:
    """格式化貨幣顯示"""
    return f"${amount:,.2f}"

def format_percentage(percentage: float) -> str:
    """格式化百分比顯示"""
    sign = "+" if percentage > 0 else ""
    return f"{sign}{percentage:.1f}%"

if __name__ == "__main__":
    # 測試用
    shopee_test = [
        {'title': 'iPhone 14 Pro 128GB', 'price': 35000, 'url': 'test1'},
        {'title': 'Samsung Galaxy S23', 'price': 25000, 'url': 'test2'}
    ]
    
    taobao_test = [
        {'title': 'iPhone 14 Pro 128G 手機', 'price': 28000, 'url': 'test3'},
        {'title': 'Samsung S23 智能手機', 'price': 20000, 'url': 'test4'}
    ]
    
    results = match_and_compare(shopee_test, taobao_test)
    for result in results:
        print(f"商品: {result['title']}")
        print(f"蝦皮價格: {format_currency(result['shopee_price'])}")
        print(f"淘寶價格: {format_currency(result['taobao_price'])}")
        print(f"價差: {format_currency(result['diff'])} ({format_percentage(result['percentage'])})")
        print(f"相似度: {result['similarity']}")
        print("-" * 50)
