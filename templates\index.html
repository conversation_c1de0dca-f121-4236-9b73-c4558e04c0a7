<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>蝦皮 vs 淘寶 價差分析工具</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft JhengHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .search-section {
            padding: 40px;
            background: #f8f9fa;
        }

        .search-form {
            display: flex;
            gap: 15px;
            max-width: 600px;
            margin: 0 auto;
        }

        .search-input {
            flex: 1;
            padding: 15px 20px;
            border: 2px solid #ddd;
            border-radius: 25px;
            font-size: 16px;
            outline: none;
            transition: border-color 0.3s;
        }

        .search-input:focus {
            border-color: #667eea;
        }

        .search-btn {
            padding: 15px 30px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            border-radius: 25px;
            font-size: 16px;
            cursor: pointer;
            transition: transform 0.3s;
        }

        .search-btn:hover {
            transform: translateY(-2px);
        }

        .search-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 40px;
        }

        .loading.show {
            display: block;
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .results-section {
            padding: 40px;
        }

        .results-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
        }

        .results-title {
            font-size: 1.8em;
            color: #333;
        }

        .results-count {
            background: #667eea;
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: bold;
        }

        .results-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .results-table th {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 15px 10px;
            text-align: left;
            font-weight: bold;
        }

        .results-table td {
            padding: 15px 10px;
            border-bottom: 1px solid #eee;
            vertical-align: top;
        }

        .results-table tr:hover {
            background: #f8f9fa;
        }

        .product-title {
            font-weight: bold;
            color: #333;
            max-width: 200px;
            word-wrap: break-word;
        }

        .price {
            font-weight: bold;
            font-size: 1.1em;
        }

        .price.shopee {
            color: #ee4d2d;
        }

        .price.taobao {
            color: #ff6600;
        }

        .price-diff {
            font-weight: bold;
        }

        .price-diff.positive {
            color: #e74c3c;
        }

        .price-diff.negative {
            color: #27ae60;
        }

        .similarity {
            background: #e8f5e8;
            color: #27ae60;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.9em;
            font-weight: bold;
        }

        .platform-link {
            display: inline-block;
            padding: 8px 16px;
            border-radius: 20px;
            text-decoration: none;
            color: white;
            font-weight: bold;
            transition: transform 0.3s;
        }

        .platform-link:hover {
            transform: translateY(-2px);
        }

        .platform-link.shopee {
            background: #ee4d2d;
        }

        .platform-link.taobao {
            background: #ff6600;
        }

        .error-message {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 10px;
            margin: 20px 0;
            border: 1px solid #f5c6cb;
        }

        .no-results {
            text-align: center;
            padding: 60px 20px;
            color: #666;
        }

        .no-results h3 {
            font-size: 1.5em;
            margin-bottom: 10px;
        }

        @media (max-width: 768px) {
            .search-form {
                flex-direction: column;
            }

            .results-table {
                font-size: 0.9em;
            }

            .results-table th,
            .results-table td {
                padding: 10px 5px;
            }

            .product-title {
                max-width: 150px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🛒 價差分析工具</h1>
            <p>比較蝦皮與淘寶台灣的商品價格，找出最佳購買選擇</p>
        </div>

        <div class="search-section">
            <form method="post" class="search-form" id="searchForm">
                <input type="text" name="keyword" value="{{ keyword }}"
                       placeholder="輸入商品名稱，例如：iPhone 14、筆記本電腦..."
                       class="search-input" required>
                <button type="submit" class="search-btn" id="searchBtn">
                    🔍 開始分析
                </button>
            </form>
        </div>

        <div class="loading" id="loading">
            <div class="spinner"></div>
            <p>正在搜尋商品資訊，請稍候...</p>
        </div>

        {% if error %}
        <div class="error-message">
            <strong>錯誤：</strong>{{ error }}
        </div>
        {% endif %}

        {% if results %}
        <div class="results-section">
            <div class="results-header">
                <h2 class="results-title">📊 分析結果</h2>
                <span class="results-count">共 {{ results|length }} 筆商品</span>
            </div>

            <table class="results-table">
                <thead>
                    <tr>
                        <th>商品名稱</th>
                        <th>蝦皮價格</th>
                        <th>淘寶價格</th>
                        <th>價差</th>
                        <th>倍率</th>
                        <th>相似度</th>
                        <th>連結</th>
                    </tr>
                </thead>
                <tbody>
                    {% for item in results %}
                    <tr>
                        <td class="product-title">{{ item['shopee_title'] }}</td>
                        <td class="price shopee">${{ "%.2f"|format(item['shopee_price']) }}</td>
                        <td class="price taobao">${{ "%.2f"|format(item['taobao_price']) }}</td>
                        <td class="price-diff {{ 'positive' if item['diff'] > 0 else 'negative' }}">
                            ${{ "%.2f"|format(item['diff']) }}
                            <br><small>({{ "%.1f"|format(item['percentage']) }}%)</small>
                        </td>
                        <td>{{ "%.2f"|format(item['ratio']) }}x</td>
                        <td><span class="similarity">{{ "%.1f"|format(item['similarity'] * 100) }}%</span></td>
                        <td>
                            {% if item['shopee_url'] %}
                            <a href="{{ item['shopee_url'] }}" target="_blank" class="platform-link shopee">蝦皮</a>
                            {% endif %}
                            <br>
                            {% if item['taobao_url'] %}
                            <a href="{{ item['taobao_url'] }}" target="_blank" class="platform-link taobao">淘寶</a>
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% elif keyword and not error %}
        <div class="no-results">
            <h3>😔 沒有找到匹配的商品</h3>
            <p>請嘗試使用不同的關鍵字或檢查網路連線</p>
        </div>
        {% endif %}
    </div>

    <script>
        document.getElementById('searchForm').addEventListener('submit', function() {
            document.getElementById('loading').classList.add('show');
            document.getElementById('searchBtn').disabled = true;
            document.getElementById('searchBtn').textContent = '分析中...';
        });
    </script>
</body>
</html>
