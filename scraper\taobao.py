import requests
from bs4 import BeautifulSoup
import time
import logging
import re
from fake_useragent import UserAgent
from typing import List, Dict, Optional
from urllib.parse import quote, urljoin

# 設定日誌
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TaobaoScraper:
    def __init__(self):
        self.base_url = "https://world.taobao.com"
        self.search_url = "https://world.taobao.com/search/search.htm"
        self.ua = UserAgent()
        self.session = requests.Session()
        
    def get_headers(self) -> Dict[str, str]:
        """獲取請求標頭"""
        return {
            'User-Agent': self.ua.random,
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-TW,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Referer': 'https://world.taobao.com/',
        }
    
    def extract_price(self, price_text: str) -> float:
        """從價格文字中提取數字"""
        if not price_text:
            return 0.0
        
        # 移除所有非數字和小數點的字符
        price_clean = re.sub(r'[^\d.]', '', price_text)
        
        try:
            return float(price_clean)
        except ValueError:
            return 0.0
    
    def search_items(self, keyword: str, limit: int = 20) -> List[Dict]:
        """搜尋淘寶台灣商品"""
        try:
            params = {
                'q': keyword,
                'tab': 'all',
                'pageNum': 1
            }
            
            headers = self.get_headers()
            
            logger.info(f"正在搜尋淘寶商品: {keyword}")
            response = self.session.get(
                self.search_url,
                params=params,
                headers=headers,
                timeout=15
            )
            
            if response.status_code == 200:
                soup = BeautifulSoup(response.content, 'html.parser')
                
                # 尋找商品容器
                items = soup.find_all('div', class_='item') or \
                       soup.find_all('div', class_='product') or \
                       soup.find_all('div', {'data-spm': re.compile(r'.*')})
                
                if not items:
                    # 嘗試其他可能的選擇器
                    items = soup.find_all('div', class_=re.compile(r'item|product|goods'))
                
                results = []
                count = 0
                
                for item in items:
                    if count >= limit:
                        break
                    
                    try:
                        # 提取商品標題
                        title_elem = item.find('a', class_=re.compile(r'title|name')) or \
                                   item.find('h3') or \
                                   item.find('div', class_=re.compile(r'title|name'))
                        
                        title = ''
                        if title_elem:
                            title = title_elem.get_text(strip=True)
                        
                        # 提取價格
                        price_elem = item.find('span', class_=re.compile(r'price|money')) or \
                                   item.find('div', class_=re.compile(r'price|money')) or \
                                   item.find('em', class_=re.compile(r'price|money'))
                        
                        price = 0.0
                        if price_elem:
                            price_text = price_elem.get_text(strip=True)
                            price = self.extract_price(price_text)
                        
                        # 提取連結
                        link_elem = item.find('a', href=True)
                        url = ''
                        if link_elem:
                            href = link_elem.get('href', '')
                            if href.startswith('//'):
                                url = 'https:' + href
                            elif href.startswith('/'):
                                url = urljoin(self.base_url, href)
                            elif href.startswith('http'):
                                url = href
                        
                        # 提取圖片
                        img_elem = item.find('img')
                        image = ''
                        if img_elem:
                            src = img_elem.get('src') or img_elem.get('data-src')
                            if src:
                                if src.startswith('//'):
                                    image = 'https:' + src
                                elif src.startswith('/'):
                                    image = urljoin(self.base_url, src)
                                elif src.startswith('http'):
                                    image = src
                        
                        # 提取銷量
                        sold_elem = item.find('span', class_=re.compile(r'sold|sales')) or \
                                  item.find('div', class_=re.compile(r'sold|sales'))
                        
                        sold = 0
                        if sold_elem:
                            sold_text = sold_elem.get_text(strip=True)
                            sold_numbers = re.findall(r'\d+', sold_text)
                            if sold_numbers:
                                sold = int(sold_numbers[0])
                        
                        if title and price > 0:
                            product_info = {
                                'title': title,
                                'price': price,
                                'url': url,
                                'image': image,
                                'sold': sold,
                                'platform': 'taobao'
                            }
                            
                            results.append(product_info)
                            count += 1
                    
                    except Exception as e:
                        logger.debug(f"解析單個商品時發生錯誤: {str(e)}")
                        continue
                
                logger.info(f"成功獲取 {len(results)} 個淘寶商品")
                return results
            
            else:
                logger.error(f"淘寶請求失敗: {response.status_code}")
                return []
                
        except requests.exceptions.RequestException as e:
            logger.error(f"淘寶請求異常: {str(e)}")
            return []
        except Exception as e:
            logger.error(f"淘寶搜尋發生未知錯誤: {str(e)}")
            return []

def get_taobao_items(keyword: str, limit: int = 20) -> List[Dict]:
    """獲取淘寶商品的主要函數"""
    taobao_scraper = TaobaoScraper()
    
    # 重試機制
    max_retries = 3
    for attempt in range(max_retries):
        try:
            items = taobao_scraper.search_items(keyword, limit)
            if items:
                return items
            
            if attempt < max_retries - 1:
                logger.info(f"第 {attempt + 1} 次嘗試失敗，等待後重試...")
                time.sleep(3)  # 淘寶需要更長的等待時間
                
        except Exception as e:
            logger.error(f"第 {attempt + 1} 次嘗試失敗: {str(e)}")
            if attempt < max_retries - 1:
                time.sleep(3)
    
    logger.warning("所有重試都失敗，返回空列表")
    return []

if __name__ == "__main__":
    # 測試用
    test_keyword = "iPhone"
    results = get_taobao_items(test_keyword, 5)
    for item in results:
        print(f"商品: {item['title']}")
        print(f"價格: ${item['price']}")
        print(f"連結: {item['url']}")
        print("-" * 50)
