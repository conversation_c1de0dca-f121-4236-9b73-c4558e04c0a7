from flask import Flask, render_template, request, flash
from scraper.shopee import get_shopee_items
from scraper.taobao import get_taobao_items
from scraper.utils import match_and_compare
from scraper.logger import setup_logger, log_scraping_result, log_comparison_result
import time

app = Flask(__name__)
app.secret_key = 'your-secret-key-here'  # 在生產環境中請使用更安全的密鑰

# 設定日誌
logger = setup_logger('flask_app')

@app.route('/', methods=['GET', 'POST'])
def index():
    results = []
    keyword = ""
    error = None

    if request.method == 'POST':
        try:
            keyword = request.form.get('keyword', '').strip()

            if not keyword:
                error = "請輸入商品關鍵字"
                return render_template('index.html', keyword=keyword, results=results, error=error)

            logger.info(f"開始搜尋商品: {keyword}")
            start_time = time.time()

            # 獲取蝦皮商品
            logger.info("正在獲取蝦皮商品...")
            shopee_items = get_shopee_items(keyword, limit=15)
            log_scraping_result('蝦皮', keyword, len(shopee_items) > 0, len(shopee_items))

            # 獲取淘寶商品
            logger.info("正在獲取淘寶商品...")
            taobao_items = get_taobao_items(keyword, limit=15)
            log_scraping_result('淘寶', keyword, len(taobao_items) > 0, len(taobao_items))

            # 檢查是否有商品資料
            if not shopee_items and not taobao_items:
                error = "未找到任何商品，請嘗試其他關鍵字"
            elif not shopee_items:
                error = "未找到蝦皮商品，僅顯示淘寶商品"
            elif not taobao_items:
                error = "未找到淘寶商品，僅顯示蝦皮商品"

            # 進行商品匹配和比較
            if shopee_items and taobao_items:
                logger.info("正在進行商品匹配和價格比較...")
                results = match_and_compare(shopee_items, taobao_items)
                log_comparison_result(len(shopee_items), len(taobao_items), len(results))

                if not results:
                    error = "找到商品但無法匹配，可能商品差異太大"

            end_time = time.time()
            logger.info(f"搜尋完成，耗時: {end_time - start_time:.2f} 秒")

        except Exception as e:
            logger.error(f"搜尋過程發生錯誤: {str(e)}")
            error = f"搜尋過程發生錯誤: {str(e)}"
            results = []

    return render_template('index.html', keyword=keyword, results=results, error=error)

@app.errorhandler(404)
def not_found_error(error):
    return render_template('404.html'), 404

@app.errorhandler(500)
def internal_error(error):
    logger.error(f"內部伺服器錯誤: {str(error)}")
    return render_template('500.html'), 500

if __name__ == '__main__':
    logger.info("啟動價差分析工具...")
    app.run(debug=True, host='0.0.0.0', port=5000)
