from flask import Flask, render_template, request
from scraper.shopee import get_shopee_items
from scraper.taobao import get_taobao_items
from scraper.utils import match_and_compare

app = Flask(__name__)

@app.route('/', methods=['GET', 'POST'])
def index():
    results = []
    keyword = ""
    if request.method == 'POST':
        keyword = request.form['keyword']
        shopee_items = get_shopee_items(keyword)
        taobao_items = get_taobao_items(keyword)
        results = match_and_compare(shopee_items, taobao_items)

    return render_template('index.html', keyword=keyword, results=results)

if __name__ == '__main__':
    app.run(debug=True)
