import logging
import os
from datetime import datetime
from logging.handlers import RotatingFileHandler

def setup_logger(name: str = 'price_checker', level: int = logging.INFO) -> logging.Logger:
    """設定日誌系統"""
    
    # 建立logs目錄
    log_dir = 'logs'
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    # 建立logger
    logger = logging.getLogger(name)
    logger.setLevel(level)
    
    # 避免重複添加handler
    if logger.handlers:
        return logger
    
    # 建立formatter
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # 建立文件handler (輪轉日誌)
    log_file = os.path.join(log_dir, f'{name}.log')
    file_handler = RotatingFileHandler(
        log_file, 
        maxBytes=10*1024*1024,  # 10MB
        backupCount=5,
        encoding='utf-8'
    )
    file_handler.setLevel(level)
    file_handler.setFormatter(formatter)
    
    # 建立控制台handler
    console_handler = logging.StreamHandler()
    console_handler.setLevel(level)
    console_handler.setFormatter(formatter)
    
    # 添加handlers到logger
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)
    
    return logger

def log_scraping_result(platform: str, keyword: str, success: bool, 
                       item_count: int = 0, error_msg: str = None):
    """記錄爬蟲結果"""
    logger = logging.getLogger('price_checker')
    
    if success:
        logger.info(f"{platform} 爬蟲成功 - 關鍵字: {keyword}, 獲取商品數: {item_count}")
    else:
        logger.error(f"{platform} 爬蟲失敗 - 關鍵字: {keyword}, 錯誤: {error_msg}")

def log_comparison_result(shopee_count: int, taobao_count: int, match_count: int):
    """記錄比較結果"""
    logger = logging.getLogger('price_checker')
    logger.info(f"價格比較完成 - 蝦皮: {shopee_count}個, 淘寶: {taobao_count}個, 匹配: {match_count}個")

# 初始化主要logger
main_logger = setup_logger()
